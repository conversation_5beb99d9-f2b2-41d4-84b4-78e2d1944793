<template>
    <section class="chat-content">
        <div class="chat-content-inner">
            <!-- 加载进度条 -->
            <ProgressBar v-show="chatForm.loading" mode="indeterminate" style="height: 6px"></ProgressBar>

            <!-- 对话内容区域 -->
            <div class="conversation-inner">
                <!-- 单条消息 -->
                <section
                    class="item"
                    :class="{
                        'item-checked': message.checked,
                        'item-in-context': message.contextSelected,
                    }"
                    v-for="message in messages"
                    :key="message.id"
                >
                    <div class="item-right">
                        <!-- 用户问题 -->
                        <div class="user-query" @mouseenter="bindShowCopyInsertBtn">
                            <div class="pic">
                                <i class="pi pi-user"></i>
                            </div>
                            <div class="content">
                                <div class="content__detail">
                                    {{ message.questionContent }}
                                </div>
                            </div>
                            <span class="button-gutter flex align-items-center">
                                <div
                                    v-show="!chatList.selectArchivedItem && !share.visible"
                                    class="context-action-button mr-4"
                                    :class="{
                                        'context-selected': message.contextSelected,
                                        'context-highlight': message.contextSelected,
                                    }"
                                    @click.stop="selectMessageContext(message)"
                                    v-tooltip.top="message.contextSelected ? '从上下文中移除' : '添加到对话上下文'"
                                >
                                    <i v-if="message.contextSelected" class="pi pi-check"></i>
                                    <i v-else class="pi pi-plus"></i>
                                    <span>{{ message.contextSelected ? '已添加' : '上下文' }}</span>
                                </div>
                                <img
                                    class="mr-4"
                                    src="@/app/assets/edit-square.svg"
                                    alt=" "
                                    @click.prevent="copyToInput(message.questionContent)"
                                    v-tooltip.top="'复制入框'"
                                />
                                <img
                                    class="mr"
                                    src="@/app/assets/file-copy--indigo.svg"
                                    alt=" "
                                    @click.prevent="copy(message.questionContent, 'markdown')"
                                    v-tooltip.top="'复制问题'"
                                />
                            </span>
                            <Button
                                v-if="!share.visible"
                                class="tool-insert align-items-center"
                                icon="pi pi-pen-to-square"
                                label="复制入框"
                                text
                                raised
                                @click.prevent="copyToInput(message.questionContent)"
                                size="small"
                            ></Button>
                        </div>
                        <!-- AI回答 -->
                        <div
                            class="model-response"
                            :class="{
                                'model-response--checked': message.checked,
                                'model-response--pointer': share.visible,
                            }"
                            @click="handleAddShare(message)"
                        >
                            <div class="pic">
                                <!-- <i class="pi pi-at"></i> -->
                                <img
                                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aidev/app-navbar/ai_dev_logo_small.png"
                                    alt=""
                                    srcset=""
                                />
                            </div>
                            <div class="content">
                                <div class="markdown-body mb-2" :id="message.id" v-if="!message.newborn">
                                    <template v-if="MessageStatus.isCompleted(message.messageStatus)">
                                        <div v-html="renderMarkdown(message.answerContent || '')"></div>
                                    </template>
                                    <div v-else :class="getMessageStatusInfo(message).class">
                                        <div class="flex align-items-center gap-2">
                                            <i
                                                :class="getMessageStatusInfo(message).icon"
                                                style="font-size: 1.5rem"
                                            ></i>
                                            <div class="ml-3">
                                                <p class="font-bold m-0">
                                                    {{ getMessageStatusInfo(message).title }}
                                                </p>
                                                <p class="text-sm m-0 mb-2">
                                                    {{ getMessageStatusInfo(message).subTitle }}
                                                </p>
                                            </div>
                                        </div>
                                        <p class="m-0 mb-2 text-sm subtitle">
                                            {{ getMessageStatusInfo(message).message }}
                                        </p>
                                        <div class="error-actions">
                                            <!-- 操作按钮组 -->
                                            <div class="action-buttons flex gap-2 mb-3 mt-2">
                                                <div class="flex align-items-center">
                                                    <div
                                                        class="button-with-badge"
                                                        v-if="getMessageStatusInfo(message).showResetContext"
                                                    >
                                                        <Button
                                                            label="重置上下文"
                                                            icon="pi pi-refresh"
                                                            size="small"
                                                            class="modern-button"
                                                            severity="primary"
                                                            @click="() => clearMessageContext()"
                                                        />
                                                        <span class="recommend-badge">
                                                            <i class="pi pi-star-fill"></i>
                                                            推荐
                                                        </span>
                                                    </div>
                                                    <Button
                                                        v-if="getMessageStatusInfo(message).showResetContext"
                                                        text
                                                        size="small"
                                                        class="help-button ml-1 p-0"
                                                        v-tooltip.top="'去知识库学习'"
                                                    >
                                                        <a
                                                            href=""
                                                            target="_blank"
                                                            class="text-sm text-500 no-underline"
                                                            @click.prevent="openLink"
                                                        >
                                                            <i class="pi pi-question-circle"></i>
                                                            <span class="ml-1">什么是上下文？</span>
                                                        </a>
                                                    </Button>
                                                </div>
                                                <Button
                                                    v-if="getMessageStatusInfo(message).showRefresh"
                                                    severity="info"
                                                    label="刷新页面"
                                                    icon="pi pi-refresh"
                                                    size="small"
                                                    class="modern-button"
                                                    @click="() => refreshPage()"
                                                />
                                                <Button
                                                    v-if="getMessageStatusInfo(message).showRetry"
                                                    severity="success"
                                                    label="立即重试"
                                                    icon="pi pi-reply"
                                                    size="small"
                                                    class="modern-button"
                                                    @click="
                                                        () => {
                                                            chatForm.data.message = message.questionContent;
                                                            sendMessage();
                                                        }
                                                    "
                                                />
                                            </div>

                                            <!-- 管理员联系区域 -->
                                            <div
                                                v-if="getMessageStatusInfo(message).showAdminInfo"
                                                class="admin-contact flex align-items-center gap-2"
                                            >
                                                <span class="text-sm text-500">
                                                    <i class="pi pi-users text-primary-400 mr-1"></i
                                                    >还有疑问？联系管理员：
                                                </span>
                                                <div class="admin-list flex gap-2">
                                                    <Tag
                                                        v-for="admin in getMessageStatusInfo(message).adminInfo"
                                                        :key="admin.name"
                                                        :value="'@' + admin.name"
                                                        severity="secondary"
                                                        class="admin-tag cursor-pointer text-sm"
                                                        @click="handleAdminClick(admin)"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="markdown-body mb-4" :id="message.id" v-else>
                                    <i class="pi pi-spinner pi-spin"></i>
                                </div>
                                <div class="answer-tools flex gap-4 align-items-center" v-show="!message.sending">
                                    <div
                                        v-show="!chatList.selectArchivedItem && !share.visible"
                                        class="context-action-button"
                                        :class="{
                                            'context-selected': message.contextSelected,
                                            'context-highlight': message.contextSelected,
                                        }"
                                        @click.stop="selectMessageContext(message)"
                                        v-tooltip.top="message.contextSelected ? '从上下文中移除' : '添加到对话上下文'"
                                    >
                                        <i v-if="message.contextSelected" class="pi pi-check"></i>
                                        <i v-else class="pi pi-plus"></i>
                                        <span>{{ message.contextSelected ? '已添加' : '上下文' }}</span>
                                    </div>
                                    <div
                                        @click.stop="copy(message.answerContent, 'markdown')"
                                        class="flex align-items-center text-slate-500 hover:text-blue-600 cursor-pointer"
                                    >
                                        <i class="pi pi-clipboard mr-1"></i>
                                        <span class="text-sm font-medium">复制MD</span>
                                    </div>
                                    <div
                                        @click.stop="copy(message.answerContent, 'text')"
                                        class="flex align-items-center text-slate-500 hover:text-blue-600 cursor-pointer"
                                    >
                                        <i class="pi pi-clipboard mr-1"></i>
                                        <span class="text-sm font-medium">复制回答</span>
                                    </div>
                                    <div
                                        @click.stop="bindShowShareCheckbox(message)"
                                        class="flex align-items-center text-slate-500 hover:text-blue-600 cursor-pointer"
                                    >
                                        <i class="pi pi-share-alt mr-1"></i>
                                        <span class="text-sm font-medium">分享</span>
                                    </div>
                                    <div class="ml-auto mr-4 flex align-items-center">
                                        <i class="pi pi-sparkles mr-1 text-slate-500 text-xs"></i>
                                        <span
                                            :style="{
                                                color: temperatureConfig(message.temperature).color,
                                            }"
                                            class="text-sm font-medium leading-normal"
                                            >回复温度：{{ message.temperature }}</span
                                        >
                                    </div>
                                    <div class="flex align-items-center" style="max-width: 220px">
                                        <i class="pi pi-at mr-1 text-slate-500 text-xs"></i>
                                        <span
                                            class="text-sm text-slate-500 font-medium whitespace-nowrap overflow-hidden text-ellipsis inline-block"
                                        >
                                            回复模型：{{ message.answerRole }}
                                        </span>
                                    </div>
                                </div>
                                <div v-show="!message.sending && !!message.associatedQuestions" class="suggestion">
                                    <h6>您可能还想问：</h6>
                                    <ol>
                                        <li v-for="item in (message.associatedQuestions || '').split('|')" :key="item">
                                            <Button @click.prevent="selectSuggestion(item)" :label="item" text />
                                        </li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- 底部输入区域 -->
            <div class="user-input">
                <!-- 聊天输入区 -->
                <div class="user-input-chat" v-show="!share.visible">
                    <!-- 功能按钮区 -->
                    <div class="action-buttons" v-show="!chatList.selectArchivedItem">
                        <ul>
                            <li class="mb-0">
                                <div class="splitbutton-badge-wrapper">
                                    <SplitButton
                                        :icon="clearContextLoading ? 'pi pi-refresh pi-spin' : 'pi pi-refresh'"
                                        v-tooltip.top="'立即取消所有上下文的选中状态，方便您和AI进行新的话题'"
                                        label="重置上下文"
                                        @click="clearMessageContext"
                                        :model="contextButtons"
                                        size="small"
                                        severity="primary"
                                    >
                                        <template #item>
                                            <div
                                                class="auto-context"
                                                v-tooltip.top="'选择后，每次会自动把新对话加入上下文'"
                                                @click.stop="onCheckboxClick($event)"
                                            >
                                                <Checkbox
                                                    v-model="this.appConfig.autoContext"
                                                    inputId="auto-context"
                                                    :binary="true"
                                                />
                                                <label for="auto-context">自动递增</label>
                                            </div>
                                        </template>
                                    </SplitButton>
                                    <span
                                        class="custom-badge flex justify-content-center align-items-center cursor-pointer"
                                        v-tooltip.top="'点击查看当前已选' + contextNum + '条上下文'"
                                        @click="showContextList = true"
                                    >
                                        {{ contextNum }}
                                    </span>
                                </div>
                            </li>
                            <li class="mb-0">
                                <Button
                                    class="modern-button"
                                    @click="handleOpenPromptShop"
                                    icon="pi pi-shop"
                                    severity="info"
                                    size="small"
                                    label="指令商店"
                                />
                            </li>
                            <li class="mb-0">
                                <Button
                                    class="modern-button"
                                    label="我的指令"
                                    icon="pi pi-list"
                                    size="small"
                                    severity="success"
                                    @click="callUpPlugins"
                                />
                            </li>
                            <!-- ==========【翻译工具按钮 - 已注释】========== -->
                            <!-- 
                            <li>
                                <Button
                                    class="modern-button"
                                    label="翻译工具"
                                    icon="pi pi-language"
                                    size="small"
                                    v-tooltip.top="'打开AI翻译工具'"
                                    @click="openTranslator"
                                    severity="info"
                                />
                            </li>
                            -->
                            <!-- ==========【流程图工具按钮 - 已注释】========== -->
                            <!--
                            <li>
                                <Button
                                    class="modern-button"
                                    label="流程图工具"
                                    icon="pi pi-sitemap"
                                    size="small"
                                    v-tooltip.top="'打开Mermaid生成器'"
                                    @click="openMermaidGenerator"
                                    severity="danger"
                                />
                            </li>
                            -->
                            <li>
                                <Button
                                    class="modern-button"
                                    @click="confirmArchiveMessage"
                                    icon="pi pi-save"
                                    label="归档会话"
                                    :disabled="messages.length === 0"
                                    v-tooltip.top="'将对话归档起来，在左侧归档会话列表可以反复查看'"
                                    size="small"
                                    severity="help"
                                />
                            </li>
                            <li class="settingInfo mb-0">
                                <OverlayPanel ref="op" style="width: 500px">
                                    <div class="form-group flex align-items-center justify-content-between mb-4">
                                        <label class="w-2">
                                            模型
                                            <a
                                                @click="reportUserEvent('clickfaq')"
                                                href="/app-product-gallery/faq"
                                                target="_blank"
                                            >
                                                <i
                                                    class="pi pi-question-circle"
                                                    v-tooltip.top="
                                                        '不同的模型，拥有不同的语言及逻辑推理能力，可点击修改会话的按钮来修改模型'
                                                    "
                                                ></i>
                                            </a>
                                        </label>
                                        <div class="flex-1">
                                            <Dropdown
                                                v-model="session.modelName"
                                                :options="availableModels"
                                                placeholder="请选择模型..."
                                                @change="handleSettingChange"
                                                :pt="{ root: 'w-full' }"
                                            />
                                        </div>
                                    </div>
                                    <div class="form-group flex justify-content-between mb-4">
                                        <label class="w-2">
                                            温度
                                            <a
                                                @click="reportUserEvent('clickfaq')"
                                                href="/app-product-gallery/faq"
                                                target="_blank"
                                            >
                                                <i
                                                    class="pi pi-question-circle"
                                                    v-tooltip.top="
                                                        '温度越低，生成策略越保守，反之生成策略越多样化，可点击修改会话的按钮来修改温度'
                                                    "
                                                ></i>
                                            </a>
                                        </label>
                                        <div class="flex-1 flex flex-wrap align-items-center">
                                            <Slider
                                                v-model="session.temperature"
                                                class="w-10"
                                                :min="0"
                                                :max="1"
                                                :step="0.01"
                                                @slideend="handleSettingChange"
                                                :pt="{
                                                    range: {
                                                        style: {
                                                            background: temperatureConfig(session.temperature).color,
                                                        },
                                                    },
                                                }"
                                            />
                                            <span
                                                class="ml-3 font-bold"
                                                :style="{
                                                    color: temperatureConfig(session.temperature).color,
                                                }"
                                                >{{ session.temperature }}</span
                                            >
                                            <MeterGroup :value="meterGroupValue" :max="100" class="w-10 mt-3">
                                                <template #label>
                                                    <div class="flex w-full justify-content-between align-items-center">
                                                        <span
                                                            v-for="item in meterGroupValue"
                                                            :key="item.label"
                                                            :style="{
                                                                color: item.color,
                                                            }"
                                                        >
                                                            {{ item.label }}
                                                        </span>
                                                    </div>
                                                </template>
                                            </MeterGroup>
                                        </div>
                                    </div>
                                </OverlayPanel>
                                <Button
                                    @click="handleClickSetting"
                                    v-tooltip.top="'模型与温度设置'"
                                    severity="warning"
                                    size="small"
                                    class="model-button modern-button"
                                >
                                    <div class="flex w-full h-full align-items-center">
                                        <i class="pi pi-cog mr-2"></i>
                                        <span class="m-0 font-medium">模型设置</span>
                                    </div>
                                </Button>
                            </li>
                        </ul>
                    </div>

                    <!-- 输入表单 -->
                    <form class="form" v-show="formVisible()" @submit.prevent="sendMessage">
                        <div class="form-inner">
                            <section class="header" v-if="showPlugis">
                                <div class="card mb-0 relative">
                                    <TabMenu :model="MenuItems" v-model:activeIndex="pluginsActiveIndex" />
                                    <i
                                        class="pi pi-times-circle absolute close-icon"
                                        v-tooltip.top="'关闭指令中心'"
                                        @click.prevent="showPlugis = false"
                                    ></i>
                                </div>
                                <div class="prompts fadein" v-show="pluginsActiveIndex === 1">
                                    <Plugins
                                        @selectPrompt="handleSelectPrompt"
                                        @openPromptShop="handleOpenPromptShop"
                                    />
                                </div>

                                <div class="history fadein" v-show="pluginsActiveIndex === 0">
                                    <ul v-if="history.data" class="ul__history">
                                        <li
                                            v-for="(item, index) in history.data"
                                            @click="bindSelectHistory(item)"
                                            :key="item"
                                        >
                                            <span style="color: #26244ce0" class="text-sm"
                                                >{{ index + 1 }}、{{ item }}</span
                                            >
                                        </li>
                                    </ul>
                                    <div class="history-empty" v-else>
                                        <i class="pi pi-face-smile text-3xl"></i>
                                        <p>暂无历史记录</p>
                                    </div>
                                </div>
                            </section>
                            <section class="main">
                                <div class="input-area" @click.prevent="focusMessage">
                                    <textarea
                                        ref="chatInput"
                                        id="chat_input"
                                        name="message"
                                        :placeholder="placeholderShortcuts"
                                        v-model="chatForm.data.message"
                                        @input.prevent="autoHeightChatForm"
                                        @keydown="sendMessageByKeyboard"
                                        @compositionstart="handleCompositionStart"
                                        @compositionend="handleCompositionEnd"
                                        @focus="handleInputFocus"
                                        @blur="handleInputBlur"
                                        :class="{ 'textarea-focused': isInputFocused }"
                                    />
                                </div>
                                <div class="sending">
                                    <Button
                                        v-if="chatForm.stop.visibility"
                                        icon="pi pi-stop-circle"
                                        severity="danger"
                                        v-tooltip.top="'停止对话'"
                                        rounded
                                        @click.prevent="stopSendMessage"
                                    ></Button>
                                    <Button
                                        v-if="chatForm.data.message?.length > 0 && !chatForm.stop.visibility"
                                        type="submit"
                                        severity="secondary"
                                        icon="pi pi-send"
                                        rounded
                                        @click.prevent="sendMessage"
                                    >
                                    </Button>
                                </div>
                                <!-- 全屏编辑 (Start) -->
                                <div class="full-screen">
                                    <Transition name="fullScreenEdit">
                                        <Button
                                            class="full-screen-edit"
                                            icon="pi pi-window-maximize"
                                            v-tooltip.top="'全屏编辑'"
                                            text
                                            severity="secondary"
                                            rounded
                                            v-show="editFullScreen.button.visible"
                                            @click="editFullScreen.dialog.visible = true"
                                        >
                                        </Button>
                                    </Transition>
                                    <Sidebar
                                        class="input-area-fullScreen"
                                        header="全屏编辑中..."
                                        position="full"
                                        v-model:visible="editFullScreen.dialog.visible"
                                    >
                                        <template #closeicon>
                                            <Button
                                                icon="pi pi-window-minimize"
                                                v-tooltip.top="'退出'"
                                                text
                                                severity="secondary"
                                                rounded
                                                v-show="editFullScreen.button.visible"
                                            >
                                            </Button>
                                        </template>
                                        <Textarea
                                            :placeholder="placeholderShortcuts"
                                            v-model="chatForm.data.message"
                                            @input.prevent="autoHeightChatForm"
                                            @keydown="sendMessageByKeyboard"
                                            @blur="handleTextareaBlur"
                                            @focus.prevent="handleTextareaFocus"
                                            @compositionstart="handleCompositionStart"
                                            @compositionend="handleCompositionEnd"
                                            :class="{ 'textarea-focused': isInputFocused }"
                                            autoResize
                                        />
                                    </Sidebar>
                                </div>
                                <!-- 全屏编辑 (End) -->
                            </section>
                        </div>
                    </form>

                    <!-- 免责声明 -->
                    <div class="disclaimer font-medium">
                        <p>
                            为确保信息安全，严禁发送
                            <a href="http://athm.cn/fQGg57c" target="_blank"> 敏感数据 </a>
                            ，请严格准守<a href="http://athm.cn/fQGg57c" target="_blank"> 《AIDev使用规范》！ </a>
                        </p>
                    </div>
                    <Button
                        v-show="showBackToBottom && userScrolled"
                        class="backToBottom-btn animated-button"
                        icon="pi pi-chevron-down"
                        rounded
                        @click="clickToBottom"
                        size="small"
                        v-tooltip.top="'返回最新对话'"
                    >
                    </Button>
                </div>

                <!-- 分享模式输入区 -->
                <div class="user-input-share" v-show="share.visible">
                    <div class="font-bold">
                        单击AI回复内容，即可选中会话。已选中
                        <span style="color: #6366f1">{{ shareMessageNum }}</span>
                        条会话
                    </div>
                    <div class="share-btn mt-3">
                        <Button
                            label="取消"
                            raised
                            severity="secondary"
                            @click="bindCancelShare"
                            class="mr-6"
                            size="small"
                        />
                        <Button
                            label="分享"
                            raised
                            :loading="share.dialog.loading"
                            @click="bindCreateShare"
                            :disabled="shareMessageNum === 0"
                            size="small"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- 分享对话框 -->
        <Dialog
            v-model:visible="share.dialog.visible"
            modal
            header="  "
            :style="{ width: '75vw', height: '60vh', background: '#fff' }"
            :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
            dismissableMask
        >
            <section style="padding-bottom: 2rem; position: sticky; top: 0px; background: #fff; z-index: 99">
                <InputGroup>
                    <InputText disabled v-model="share.dialog.link" />
                    <Button label="复制链接" @click="copy(share.dialog.link, 'text')" />
                </InputGroup>
            </section>
            <section>
                <ChatHistoryCom :messages="share.dialog.data" />
            </section>
        </Dialog>

        <!-- 添加上下文列表浮窗 -->
        <Dialog
            v-model:visible="showContextList"
            modal
            header="当前上下文列表"
            :style="{ width: '500px' }"
            :breakpoints="{ '575px': '90vw' }"
            dismissableMask
        >
            <div class="context-list-container">
                <div v-if="contextMessages.length > 0">
                    <div v-for="(msg, index) in contextMessages" :key="msg.id" class="context-list-item p-3 mb-2">
                        <div class="flex align-items-center justify-content-between">
                            <span class="context-index">{{ index + 1 }}</span>
                            <div class="context-content flex-1 mx-3">{{ msg.questionContent }}</div>
                            <Button
                                icon="pi pi-times"
                                class="p-button-rounded p-button-text p-button-sm"
                                @click="selectMessageContext(msg)"
                                v-tooltip.top="'从上下文中移除'"
                            />
                        </div>
                    </div>
                </div>
                <div v-else class="p-4 text-center">
                    <i class="pi pi-info-circle text-xl mb-3" style="color: var(--text-color-secondary)"></i>
                    <p>当前没有选中的上下文消息</p>
                </div>
            </div>
        </Dialog>
        <!-- 弹窗 : 分享 (End) -->

        <!-- ==========【翻译工具对话框 - 已注释】========== -->
        <!--
        <Dialog
            v-model:visible="translator.dialog.visible"
            modal
            header="AI翻译工具"
            :style="{ width: '90vw', maxWidth: '900px', minHeight: '500px' }"
            :breakpoints="{ '1199px': '75vw', '575px': '95vw' }"
            dismissableMask
        >
            <Translator />
        </Dialog>
        -->

        <!-- ==========【流程图工具对话框 - 已注释】========== -->
        <!--
        <Dialog
            v-model:visible="mermaidGenerator.dialog.visible"
            modal
            header="Mermaid生成器"
            :style="{ width: '95vw', maxWidth: '1000px', minHeight: '600px' }"
            :breakpoints="{ '1199px': '85vw', '575px': '98vw' }"
            dismissableMask
        >
            <MermaidGenerator />
        </Dialog>
        -->

        <!-- 回复完成通知 -->
        <Notification
            v-model:visible="notification.visible"
            :title="notification.title"
            :subtitle="notification.subtitle"
            @click="handleNotificationClick"
            @close="closeNotification"
        />
    </section>
</template>

<script>
import { mapState, mapActions, mapWritableState } from 'pinia';
import useAppStore from '@/app/stores/app';
import useChatStore from '@/app/stores/chat';
import useChatvmStore from '@/app/stores/chatvm';
import useShareStore from '@/app/stores/share';
import { DiffDOM } from 'diff-dom';
import { md, fixMarkdownCodeBlocks, handleMarkdownImages } from '@/app/utils/markdown';
import storage from '@/app/utils/storage';

import { ChatHistory } from '@/app/utils/index';
const chatHistory = new ChatHistory('app-chatgpt-history');
chatHistory.loadHistory();
const promptsHistory = new ChatHistory('app-chatgpt-prompts-history');

import { scrollToBottom, formatDate, checkShortcuts, copyToClipboard, throttle } from '@/app/utils/index';

import ChatHistoryCom from '@/pages/components/ChatHistory/index.vue';
import Plugins from '../components/Plugins.vue';
import Notification from '../components/Notification.vue';
import { MessageStatus } from '@/utils/MessageStatus';
// ==========【翻译工具和流程图工具组件导入 - 已注释】==========
// import Translator from '@/pages/components/Translator/index.vue';
// import MermaidGenerator from '@/pages/components/MermaidGenerator/index.vue';

export default {
    name: 'PageHomeContent',
    components: {
        ChatHistoryCom,
        Plugins,
        Notification,
        // ==========【翻译工具和流程图工具组件注册 - 已注释】==========
        // Translator,
        // MermaidGenerator,
    },
    data() {
        return {
            clearContextLoading: false,
            scrollPanelRef: null,
            pluginsActiveIndex: 1,
            MenuItems: [
                { label: '历史提问', icon: 'pi pi-history' },
                { label: '快捷指令', icon: 'pi pi-shop' },
            ],
            share: {
                visible: false,
                dialog: {
                    loading: false,
                    visible: false,
                    link: '',
                    data: [],
                },
            },
            // ==========【翻译工具和流程图工具数据 - 已注释】==========
            // translator: {
            //     dialog: {
            //         visible: false,
            //     },
            // },
            // mermaidGenerator: {
            //     dialog: {
            //         visible: false,
            //     },
            // },
            // 全屏编辑
            editFullScreen: {
                button: {
                    visible: false,
                },
                dialog: {
                    visible: false,
                },
            },
            sse: '',
            knowledgeId: false,
            contextButtons: [
                {
                    label: '关闭自动递增',
                    icon: 'pi pi-refresh',
                    class: 'context-setting',
                    command: () => {},
                },
            ],
            history: {
                visible: false,
                data: null,
            },
            prompts: {
                visible: false,
            },
            meterGroupValue: [
                { label: '精确(0 - 0.3)', color: '#007bff', value: 30 },
                { label: '平衡(0.3 - 0.7)', color: '#28a745', value: 40 },
                { label: '创意(0.7 - 1)', color: '#dc3545', value: 30 },
            ],
            showPlugis: false,
            // 滚动相关状态 - 简化版
            showBackToBottom: false,
            userScrolled: false, // 用户是否手动滚动过
            lastScrollTop: 0, // 记录上一次的滚动位置，用于判断滚动方向
            MessageStatus, // 将 MessageStatus 类添加到 data 中。 Vue 模板中只能访问到组件实例上的属性， 所以我们需要将 MessageStatus 类添加到组件的 data 中使其成为组件实例的一部分。
            isInputFocused: false, // 输入框是否聚焦
            formInnerFocused: false, // form-inner 是否聚焦
            showContextList: false, // 是否显示上下文列表浮窗
            notification: {
                visible: true,
                title: 'AI 回答已完成',
                subtitle: '点击查看最新回复',
            }, // 回复完成通知
        };
    },
    computed: {
        ...mapState(useAppStore, ['appConfig', 'noLoginRequired']),
        ...mapWritableState(useChatStore, ['session', 'messages', 'settingPopover', 'availableModels']),
        ...mapWritableState(useChatvmStore, ['chatList', 'chatForm']),
        ...mapWritableState(useShareStore, {
            message: 'shareMessage',
        }),
        placeholderShortcuts() {
            let result = '';
            switch (this.appConfig.shortcuts.sendChatMessage) {
                case '1':
                    result = 'Enter发送, Shift+Enter换行.';
                    break;
                case '2':
                    result = 'Enter换行, Crtl+Enter发送.';
                    break;
                case '3':
                    result = 'Enter发送, Crtl+Enter换行.';
                    break;
                default:
                    result = 'Enter发送, Shift+Enter换行.';
            }
            result = "发消息、输入'/'选择指令、输入'ctrl+Shift+↑'回显历史提问";
            return result;
        },
        contextNum() {
            if (this.session.contextIds != undefined) {
                return this.session.contextIds.length;
            }
            return 0;
        },
        // 统计分享时，被选中的会话条数 换个名字
        shareMessageNum() {
            return this.messages.filter((item) => item.checked).length;
        },
        // 获取上下文消息列表
        contextMessages() {
            return this.messages.filter((msg) => msg.contextSelected);
        },
    },
    watch: {
        // 监听 chatForm.data.message ，当值为 ' ' 是，弹出指令
        'chatForm.data.message'(newVal) {
            if (newVal === '/') {
                this.showPlugis = true;
                this.pluginsActiveIndex = 1;
                if (storage.getLocalStorage('app-chatgpt-history')) {
                    this.history.data = storage.getLocalStorage('app-chatgpt-history').reverse();
                }
                this.reportUserEvent('click_appChatgpt_history_magicSlash');
            } else {
                this.showPlugis = false;
            }
        },
    },
    methods: {
        ...mapActions(useAppStore, ['setAppConfig', 'reportUserEvent']),
        ...mapActions(useChatStore, [
            'createMessage',
            'getMessageIds',
            'getMessages',
            'getMessagesArchived',
            'updateArchivedMessage',
            'updateMessageContextSelected',
            'clearMessageContextSelected',
            'getPrompts',
            'updateSession',
        ]),
        ...mapActions(useChatvmStore, ['stopSendMessage', 'chatForm']),
        ...mapActions(useShareStore, ['getShareMessage', 'createShareMessage']),
        formatDate,
        temperatureConfig(temperature) {
            // 按照温度进行颜色区分，跟设置页一致
            if (temperature >= 0 && temperature < 0.3) {
                return {
                    color: '#007bff',
                };
            } else if (temperature >= 0.3 && temperature < 0.7) {
                return {
                    color: '#28a745',
                };
            } else {
                return {
                    color: '#dc3545',
                };
            }
        },
        // 快速调整温度与模型
        handleClickSetting(event) {
            this.$refs.op.toggle(event);
        },
        //保存温度与模型的更新
        async handleSettingChange() {
            const { id, modelName, sessionType, sessionName, systemMessage, temperature } = this.session;
            const data = {
                id,
                modelName,
                sessionName,
                sessionType,
                systemMessage,
                temperature,
            };
            try {
                await this.updateSession(data);
                this.$toast.add({
                    severity: 'success',
                    summary: '调整参数成功',
                    detail: '',
                    life: 1000,
                });
            } catch (err) {
                this.$toast.add({
                    severity: 'error',
                    summary: '调整参数失败，请重试~',
                    detail: err.message,
                    life: 1000,
                });
                return;
            }
        },

        callUpPlugins() {
            if (this.showPlugis) return;

            this.showPlugis = true;

            // 拿到历史记录的数据
            if (storage.getLocalStorage('app-chatgpt-history')) {
                this.history.data = storage.getLocalStorage('app-chatgpt-history').reverse();
            }

            this.autoHeightChatForm();
        },

        // ==========【翻译工具和流程图工具方法 - 已注释】==========
        // 打开翻译工具
        // openTranslator() {
        //     this.translator.dialog.visible = true;
        //     this.reportUserEvent('click_appChatgpt_translator_open');
        // },

        // 打开流程图工具
        // openMermaidGenerator() {
        //     this.mermaidGenerator.dialog.visible = true;
        //     this.reportUserEvent('click_appChatgpt_mermaid_open');
        // },

        // 将选中提示词插入到输入框
        handleSelectPrompt(prompt) {
            // 去掉html标签 :  输入 "<strong>abc</strong>efg"  -> 输出 "abcefg"
            const reg = /<\/?[^>]+>/g;
            this.chatForm.data.message = prompt.content.replace(reg, '');
            this.autoHeightChatForm();
            this.showPlugis = false;

            this.$nextTick(this.focusToInput);
        },
        // 调用 navBar 的 提示词百宝箱
        handleOpenPromptShop() {
            const myEvent = new CustomEvent('app-chatgpt-openPromptBox');
            window.dispatchEvent(myEvent);
            this.showPlugis = false;
        },
        formVisible() {
            let result = true;
            if (this.chatList.selectArchivedItem || this.session.modelName == 'gpt-4-32k') {
                result = false;
            }
            return result;
        },
        handleCompositionStart() {
            this.chatForm.isCompositionEnd = false;
        },
        handleCompositionEnd() {
            this.chatForm.isCompositionEnd = true;
        },
        handleTextareaBlur() {
            this.showPlugis = false;
        },
        handleTextareaFocus() {
            // 此时输入框只有一个/ ，展示指令
            if (this.chatForm.data.message === '/') {
                this.showPlugis = true;
                this.pluginsActiveIndex = 1;
            }
        },
        handleInputFocus() {
            this.isInputFocused = true;
            this.formInnerFocused = true;

            // 获取form-inner元素并添加聚焦类
            const formInner = document.querySelector('.form-inner');
            if (formInner) {
                formInner.classList.add('form-inner-focus');
            }

            // 记录用户交互事件
            this.reportUserEvent('focus_appChatgpt_input');
        },
        handleInputBlur() {
            this.isInputFocused = false;

            // 移除form-inner聚焦类
            setTimeout(() => {
                const formInner = document.querySelector('.form-inner');
                if (formInner) {
                    formInner.classList.remove('form-inner-focus');
                }
                this.formInnerFocused = false;
            }, 100);
        },
        async sendMessage() {
            const values = this.chatForm.data;
            const sessionId = this.session.id;
            // 输入框为空时，不发送
            if (values.message?.trim().length === 0) return;
            // 正在返回消息的过程中, 不允许发送
            if (this.chatForm.submit.disabled) return;
            // 如果当前在全屏模式、退出全屏模式
            if (this.editFullScreen.dialog.visible) {
                this.editFullScreen.dialog.visible = false;
                this.editFullScreen.button.visible = false;
            }
            // 回车
            // 中文输入法检测 : 当中文输入法里面按回车的时候, 不发送信息
            if (this.chatForm.isCompositionEnd) {
                if (!sessionId) {
                    this.$toast.add({
                        severity: 'error',
                        summary: '警告',
                        detail: '请先创建会话',
                        life: 3000,
                    });
                    return;
                }
                this.chatForm.submit.disabled = true;
                const body = {
                    questionContent: values.message,
                    // TODO : 做个兼容处理, 线上有选择全部的同学, 会报错
                    contextQuestionIds: [],
                    sessionId,
                    messageType: 0,
                    // 温度
                    temperature: this.session.temperature,
                    knowledgeId: this.knowledgeId ? 1 : 0,
                    answerRole: '', // 当前字段不起作用, 服务端根据sessionId自行判断
                };
                let data;
                try {
                    data = await this.createMessage(body);
                } catch (err) {
                    this.$toast.add({
                        severity: 'error',
                        summary: '错误',
                        detail: err.message,
                        life: 3000,
                    });
                    this.chatForm.submit.disabled = false;
                    return;
                }

                // 将当前输入框内容, 写入localStorage, 后续用于用户查看历史记录
                chatHistory.addToHistory(this.chatForm.data.message);
                chatHistory.index = -1;

                this.chatForm.data.message = '';
                this.chatForm.stop.visibility = true;

                // 确保在清空输入框内容后，强制重置高度
                this.$nextTick(() => {
                    this.autoHeightChatForm();
                });

                const enableSuggestParams =
                    this.appConfig.enableSuggest === '0' ? 'enableSuggest=false' : 'enableSuggest=true';
                const url2 = `/api/sendMessage?messageId=${data.data.id}&${enableSuggestParams}`;

                const source = new EventSource(url2);
                // 将重连间隔设置为一个非常大的值（例如24小时）
                source.retry = 24 * 60 * 60 * 1000;
                this.chatForm.eventSource = source;

                const message = data.data;

                message.newborn = true;
                message.sending = true;

                const messageLenth = this.messages.length;
                this.messages[messageLenth] = message;

                this.$nextTick(() => {
                    this.userScrolled = false;
                    this.autoScrollToBottom();
                });

                source.onopen = (err) => {
                    console.log('open', err);
                    const _message = this.messages[messageLenth];
                    _message.answerContent = '';

                    this.$nextTick(() => {
                        document.getElementById(_message.id).innerHTML = '';
                    });
                    // this.autoScrollToBottom();
                };

                source.onmessage = async (e) => {
                    const data = JSON.parse(e.data);
                    const _message = this.messages[messageLenth];
                    _message.answerContent = data?.answerContent || '';

                    this.$nextTick(() => {
                        const dd = new DiffDOM();
                        const oldDom = document.getElementById(_message.id);
                        const newDom = document.createElement('div');
                        Array.from(oldDom.attributes).forEach((attr) => {
                            newDom.setAttribute(attr.name, attr.value);
                        });
                        newDom.innerHTML = this.renderMarkdown(_message.answerContent);
                        const diff = dd.diff(oldDom, newDom);
                        // 应用差异
                        dd.apply(oldDom, diff);

                        // 智能滚动：只有当用户没有手动滚动时才自动滚动
                        if (!this.userScrolled) {
                            this.autoScrollToBottom();
                        }
                    });
                    _message.answerTime = data.answerTime;
                    _message.associatedQuestions = data.associatedQuestions;
                    _message.messageStatus = data.messageStatus;
                    _message.contextSelected = data.contextSelected;
                    if (data.messageStatus == 5 && this.appConfig.autoContext) {
                        await this.updateMessageContextSelected({
                            messageId: data.id,
                            sessionId: data.sessionId,
                            contextSelected: true,
                        });
                    }
                };

                // mdn（https://developer.mozilla.org/zh-CN/docs/Web/API/EventSource）上，没有 end 事件。
                // source.addEventListener('end', () => {
                //     console.log(123);

                // });

                // 正确的请求完成后，也会调用 error。
                source.onerror = (err) => {
                    console.log('onerror', err);
                    this.chatForm.submit.disabled = false;
                    source.close();
                    this.chatForm.stop.visibility = false;
                    const _message = this.messages[messageLenth];

                    // Todo :
                    //  服务端目前没有"消息发送完成"的标识, 所以有的时候, 消息返回结束了,但推荐内容还没返回.
                    setTimeout(() => {
                        _message.sending = false;
                        _message.newborn = false;

                        // 如果用户不在底部，显示友好提示
                        if (!this.isAtBottom()) {
                            this.showReplyCompleteNotification();
                        }
                    }, 0);
                };
            }
        },
        sendMessageByKeyboard(event) {
            const configShotcuts = this.appConfig.shortcuts.sendChatMessage;
            let result;
            // 全局配置快捷键后, 需要检查用户的输入 (正常输入, 换行, 回车, 魔方斜杠, 查看历史记录[ctrl+上下] )
            result = checkShortcuts(configShotcuts, event);
            if (result.history.status) {
                let direction;
                direction = result.history.key === 'ArrowUp' ? -1 : 1;
                this.chatForm.data.message = chatHistory.handleHistory(direction);
                /**
                 * Issue Fix : Windows下, 使用Ctrl + Shift + 上箭头时, 会选中文本.
                 *   https://git.corpautohome.com/dealer-arch/microfrontends-ai/root-config/issues/216
                 */
                this.$nextTick(() => {
                    if (result.history.key === 'ArrowUp') {
                        const start = event.target.selectionStart;
                        event.target.setSelectionRange(start + 1, start + 1);
                        event.preventDefault();
                    }
                });
                // Issue Fix (End)
                this.autoHeightChatForm();
                this.reportUserEvent(`click_appChatgpt_history_${result.history.key}`);
                return;
            }

            if (!result.enter && !result.newline) {
                return;
            } else if (result.newline && !result.enter) {
                // 换行
                event.preventDefault();
                const start = event.target.selectionStart;
                const end = event.target.value.length;
                this.chatForm.data.message =
                    this.chatForm.data.message.slice(0, start) + '\n' + this.chatForm.data.message.slice(start);
                // 设置光标位置
                this.$nextTick(() => {
                    event.target.setSelectionRange(start + 1, start + 1);
                    this.autoHeightChatForm();
                    // 在内容尾部时, 自动滚动到底部
                    if (start === end) {
                        scrollToBottom('#chat_input');
                    }
                });
            } else {
                event.preventDefault();
                this.sendMessage();
            }
        },

        /**
         * 点击返回底部按钮
         */
        clickToBottom() {
            this.userScrolled = false;
            this.showBackToBottom = false;
            this.autoScrollToBottom('smooth');
        },

        /**
         * 自动滚动到底部
         */
        autoScrollToBottom(behavior = 'auto') {
            scrollToBottom('.chat-content-inner .conversation-inner', behavior);
        },

        /**
         * 判断是否在底部
         */
        isAtBottom() {
            const $conversation = document.querySelector('.chat-content .conversation-inner');
            if (!$conversation) return false;

            const scrollTop = $conversation.scrollTop;
            const scrollHeight = $conversation.scrollHeight;
            const clientHeight = $conversation.clientHeight;
            return scrollTop + clientHeight >= scrollHeight - 100; // 100px容差
        },

        /**
         * 处理用户滚动事件（节流）
         */
        handleUserScroll: throttle(function () {
            const $conversation = document.querySelector('.chat-content .conversation-inner');
            if (!$conversation) return;

            const currentScrollTop = $conversation.scrollTop;
            const scrollDirection = currentScrollTop > this.lastScrollTop ? 'down' : 'up';

            // 判断用户滚动意图：
            // 1. 向上滚动 = 用户想查看历史消息，设置为手动模式
            // 2. 滚动到底部 = 用户想跟上最新消息，可以恢复自动模式
            if (scrollDirection === 'up' && Math.abs(currentScrollTop - this.lastScrollTop) > 0) {
                // 向上滚动超过10px，认为用户想查看历史内容
                this.userScrolled = true;
                this.showBackToBottom = true;
                console.log('用户向上滚动，切换到手动模式');
            } else if (this.isAtBottom()) {
                // 用户滚动到底部，恢复自动模式
                this.userScrolled = false;
                this.showBackToBottom = false;
                console.log('用户滚动到底部，恢复自动模式');
            } else if (this.userScrolled && !this.isAtBottom()) {
                // 已经是手动模式且不在底部，继续显示返回按钮
                this.showBackToBottom = true;
            }

            // 加载更多历史消息
            if (currentScrollTop < 700 && !this.chatForm.loadedAllMessages) {
                this.loadMoreMessages($conversation);
            }

            // 更新上一次的滚动位置
            this.lastScrollTop = currentScrollTop <= 0 ? 0 : currentScrollTop;
        }, 100),

        /**
         * 加载更多历史消息
         */
        async loadMoreMessages($conversation) {
            const positionOld = $conversation.scrollHeight;
            this.chatForm.loading = true;
            this.chatForm.data.messages.pageIndex += 1;

            let data;
            if (this.activeIndex !== -1) {
                data = await this.getMessages(this.chatForm.data.messages);
            } else {
                data = await this.getMessagesArchived(this.chatForm.data.messages);
            }

            this.chatForm.loading = false;

            if (data?.pagecount < this.chatForm.data.messages.pageIndex) {
                this.chatForm.loadedAllMessages = true;
                return;
            } else {
                this.messages = data.list.concat(this.messages);

                this.$nextTick(() => {
                    const scrollHeight = document.querySelector('.chat-content .conversation-inner').scrollHeight;
                    $conversation.scrollTop = scrollHeight - positionOld;
                });
            }
        },

        focusMessage() {
            this.autoHeightChatForm();
            this.$refs.chatInput.focus();
        },
        // 参考 : https://stackoverflow.com/questions/17772260/textarea-auto-height
        autoHeightChatForm() {
            this.$nextTick(() => {
                // 添加空值检查
                if (!this.$refs.chatInput) {
                    return;
                }

                // 清除之前设置的高度，让浏览器计算基础高度 - 这一行很关键
                this.$refs.chatInput.style.height = 'auto';

                // 判断是否有内容，如果没有内容则设置为默认高度
                if (!this.chatForm.data.message || this.chatForm.data.message.trim() === '') {
                    this.$refs.chatInput.style.height = '56px';
                    this.editFullScreen.button.visible = false;
                } else {
                    // 根据内容设置高度
                    const scrollHeight = this.$refs.chatInput.scrollHeight;
                    this.$refs.chatInput.style.height = scrollHeight + 'px';
                    this.editFullScreen.button.visible = scrollHeight > 96;
                }
            });
        },
        renderMarkdown(markdownContent) {
            let html = md.render(fixMarkdownCodeBlocks(markdownContent));
            html = handleMarkdownImages(html);

            // 添加代码块复制按钮
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            const preElements = tempDiv.querySelectorAll('pre');
            preElements.forEach((pre) => {
                const copyButton = document.createElement('button');
                copyButton.className = 'copy-button';
                copyButton.innerHTML = '复制';
                copyButton.setAttribute('data-clipboard-action', 'copy');
                pre.appendChild(copyButton);
            });

            return tempDiv.innerHTML;
        },
        highlightContext(index) {
            const contextNum = this.appConfig.contextNum;
            return index >= this.messages.length - contextNum;
        },
        confirmArchiveMessage(event) {
            this.$confirm.require({
                target: event.currentTarget,
                message: '您确认归档会话吗?',
                icon: 'pi pi-exclamation-triangle',
                rejectClass: 'p-button-secondary p-button-outlined p-button-sm',
                acceptClass: 'p-button-sm',
                rejectLabel: '取消',
                acceptLabel: '确认',
                accept: () => {
                    this.archiveMessage();
                },
                reject: () => {
                    // this.$toast.add({ severity: 'warn', summary: 'Rejected', detail: '已取消归档会话', life: 3000 });
                },
            });
        },
        async archiveMessage() {
            try {
                await this.updateArchivedMessage(this.session.id);
                this.$toast.add({
                    severity: 'success',
                    summary: '归档成功，请在归档会话中查看',
                    detail: '',
                    life: 3000,
                });

                // 会话归档成功后，清除上下文
                await this.clearMessageContext(1);
            } catch (err) {
                this.$toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: err.message,
                    life: 3000,
                });
                return;
            }
            this.messages = [];
        },
        selectSuggestion(text) {
            this.chatForm.data.message = text;
        },
        async copy(input, type) {
            try {
                await copyToClipboard(input, type);
                this.$toast.add({
                    severity: 'success',
                    summary: '已复制到剪贴板',
                    detail: '',
                    life: 3000,
                });
            } catch (err) {
                this.$toast.add({
                    severity: 'err',
                    summary: '失败',
                    detail: err,
                    life: 3000,
                });
            }
        },
        copyToInput(input) {
            this.showPlugis = false;
            this.chatForm.data.message = input;

            // 使用 $nextTick 确保在 DOM 更新后再调整高度
            this.$nextTick(() => {
                this.autoHeightChatForm();
                // 鼠标自动聚焦到输入框最后
                this.focusToInput();
            });

            this.$toast.add({
                severity: 'success',
                summary: '复制到输入框',
                detail: '',
                life: 3000,
            });
        },

        // 鼠标自动聚焦到输入框最后
        focusToInput() {
            this.$nextTick(() => {
                const $textarea = document.querySelector('.input-area textarea');
                $textarea.focus();
                $textarea.setSelectionRange($textarea.value.length, $textarea.value.length);
            });
        },

        async selectMessageContext(message) {
            if (this.share.visible) return;
            try {
                await this.updateMessageContextSelected({
                    messageId: message.id,
                    sessionId: message.sessionId,
                    contextSelected: !message.contextSelected == 1,
                });
            } catch (err) {
                this.$toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: err.message,
                    life: 3000,
                });
                return;
            }
        },
        async clearMessageContext(isArchiveMessage) {
            // 如果此时上下文条数为0，不执行
            if (this.contextNum === 0 && isArchiveMessage !== 1) {
                this.$toast.add({
                    severity: 'warn',
                    summary: '提示',
                    detail: '上下文条数为0，无需清除',
                    life: 3000,
                });
                return;
            }
            this.clearContextLoading = true;
            try {
                await this.clearMessageContextSelected(this.session.id);
                this.clearContextLoading = false;
                this.$toast.add({
                    severity: 'success',
                    summary: '已重置上下文',
                    detail: '',
                    life: 3000,
                });
            } catch (err) {
                this.clearContextLoading = false;
                this.$toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: err.message,
                    life: 3000,
                });
                return;
            }
        },
        onCheckboxClick(event) {
            // 阻止事件冒泡
            event.stopPropagation();

            // 可以在这里处理复选框的点击事件，如果需要的话
            // 例如，可以更新 item.checked 的值
            // item.checked = !item.checked;
        },
        bindShowCopyInsertBtn(e) {
            if (this.share.visible) return;
            const eleWidth = e.currentTarget.clientWidth;
            const optimalLeft = eleWidth - e.offsetX < 120 ? eleWidth - 120 : e.offsetX - 20;
            e.currentTarget.querySelector('.tool-insert').style.left = `${optimalLeft}px`;
        },
        bindSelectHistory(message) {
            this.chatForm.data.message = message;
            this.showPlugis = false;
            this.autoHeightChatForm();
            this.$nextTick(this.focusToInput);
        },
        bindCancelShare() {
            this.share.visible = false;
            this.messages.forEach((item) => {
                item.checked = false;
            });

            // 退出分享模式,弹窗告知用户
            this.$toast.add({
                severity: 'success',
                summary: '已退出分享模式',
                detail: '',
                life: 3000,
            });
        },
        bindShowShareCheckbox(message) {
            this.share.visible = true;
            message.checked = true;
        },
        handleAddShare(message) {
            if (!this.share.visible) return;

            // 选中状态下再次点击取消选中
            message.checked = !message.checked;
        },
        async bindCreateShare() {
            const data = this.messages.filter((item) => item.checked === true);
            if (data.length < 1) {
                this.$toast.add({
                    severity: 'warn',
                    summary: '提示',
                    detail: '请勾选你要分享的内容!',
                    life: 3000,
                });
                return;
            }
            this.share.dialog.loading = true;
            this.share.dialog.data = data;
            // 创建分享
            const options = data.map((item) => item.id);
            let dataShare;
            try {
                dataShare = await this.createShareMessage(options);
                this.share.dialog.link = `${location.origin}/app-chatgpt/share/${dataShare}`;
                this.share.dialog.visible = true;
            } catch (err) {
                console.log(err);
                this.$toast.add({
                    severity: 'error',
                    summary: '错误',
                    detail: err.message,
                    life: 3000,
                });
            }
            this.share.dialog.loading = false;
            this.reportUserEvent('click_appChatgpt_share');
        },

        // 更新提示词历史记录
        updatePromptHistory(prompt) {
            promptsHistory.loadHistory();
            let historyList = [...promptsHistory.historyList];
            const index = historyList.findIndex((item) => item.id === prompt.id);
            if (index !== -1) {
                historyList.splice(index, 1);
            }
            historyList.unshift(prompt);
            promptsHistory.setHistoryList(historyList);
        },

        handlePromptsHistory(event) {
            // 去掉html标签 :  输入 "<strong>abc</strong>efg"  -> 输出 "abcefg"'
            const prompt = event.detail;
            const reg = /<\/?[^>]+>/g;
            this.chatForm.data.message = prompt.content.replace(reg, '');
            prompt.content = prompt.content.replace(reg, '');
            this.autoHeightChatForm();
            this.updatePromptHistory(prompt);
        },

        handlePageOver(event) {
            if (event.target.tagName === 'PRE') {
                // 找到 code 的兄弟元素 copy-button
                const code = event.target.querySelector('code');
                if (code) {
                    const copyButton = code.parentElement.querySelector('.copy-button');
                    if (copyButton) {
                        copyButton.style.display = 'block';
                    }
                }
            }
        },
        handlePageOut(event) {
            if (
                // 如果鼠标移出 pre 标签， 并且没有移入 pre 标签的子元素， 隐藏 copy-button
                event.target.tagName === 'PRE' &&
                event.relatedTarget &&
                !event.relatedTarget.closest('PRE')
            ) {
                const code = event.target.querySelector('code');
                if (code) {
                    const copyButton = code.parentElement.querySelector('.copy-button');
                    if (copyButton) {
                        copyButton.style.display = 'none';
                    }
                }
            }
        },
        handlePageClick(event) {
            if (event.target.matches('.copy-button')) {
                const button = event.target;
                const codeContainer = button.parentElement;
                const code = codeContainer.querySelector('code');
                if (code) {
                    this.copy(code.innerText, 'markdown');
                }
            }
        },
        getMessageStatusInfo(message) {
            return MessageStatus.getStatusInfo(message.messageStatus, message.answerContent);
        },
        // 添加新方法处理代码审查
        async handleCodeReview() {
            const codeData = localStorage.getItem('codeReviewData');
            const shouldAutoSend = localStorage.getItem('shouldAutoSendMessage') === 'true';

            if (!codeData || !shouldAutoSend) return;

            const { ruleName, problemCode, problemType } = JSON.parse(codeData);

            // 组织提示词
            const prompt = `我需要你帮我分析一段代码:
                规范名称: ${ruleName}
                问题类型: ${problemType}
                问题代码:
                \`\`\`
                ${problemCode}
                \`\`\`

                请帮我:
                1. 分析这段代码存在的具体问题
                2. 提供优化后的代码示例
                3. 说明优化的理由和收益`;

            // 设置到输入框
            this.chatForm.data.message = prompt;

            // // 自动发送消息
            await this.sendMessage();

            //  清除 localStorage 数据
            localStorage.removeItem('codeReviewData');
        },

        refreshPage() {
            window.location.reload();
        },
        openLink() {
            const docUrl = 'https://zhishi.autohome.com.cn/home/<USER>/file?targetId=12kYYQmajHE';
            if (docUrl) {
                window.open(docUrl, '_blank');
            } else {
                this.$toast.add({
                    severity: 'info',
                    summary: '文档准备中',
                    detail: '帮助文档正在准备中，请稍后再试',
                    life: 3000,
                });
            }
        },
        handleAdminClick(admin) {
            if (!admin?.dingTalkUrl) {
                this.$toast.add({
                    severity: 'info',
                    summary: '提示',
                    detail: '暂无可用的联系方式',
                    life: 3000,
                });
                return;
            }
            window.location.href = admin.dingTalkUrl;
        },

        // 显示回复完成通知
        showReplyCompleteNotification() {
            this.notification.visible = true;
            this.notification.title = 'AI 回答完成';
            this.notification.subtitle = '点击查看最新回复';
        },

        // 关闭通知
        closeNotification() {
            this.notification.visible = false;
        },

        // 处理通知点击事件
        handleNotificationClick() {
            this.closeNotification();
            this.userScrolled = false;
            this.showBackToBottom = false;
            this.autoScrollToBottom('smooth');
        },
    },
    mounted() {
        const $conversation = document.querySelector('.chat-content .conversation-inner');
        $conversation.addEventListener('scroll', this.handleUserScroll, false);

        // app-navbar 中的提示词百宝箱 - 使用提示词
        window.addEventListener('app-navbar-usePrompt', this.handlePromptsHistory);

        // 设置事件委托来处理复制按钮的点击事件
        document.addEventListener('click', this.handlePageClick);

        document.addEventListener('mouseover', this.handlePageOver);
        document.addEventListener('mouseout', this.handlePageOut);

        // 初始化输入框高度
        this.$nextTick(() => {
            if (this.$refs.chatInput) {
                this.autoHeightChatForm();
            }
        });

        // 优化路由监听逻辑
        this.$router.afterEach((to, from) => {
            // 只有从 sonar 页面跳转到 chat 页面时才执行
            const fromSonar = from.path.includes('/sonar');
            const toChat = to.path === '/';

            if (toChat && fromSonar) {
                // 传入 true 表示来自 sonar 页面
                this.handleCodeReview();
            }
        });
    },
    unmounted() {
        window.removeEventListener('app-navbar-usePrompt', this.handlePromptsHistory);
        document.removeEventListener('click', this.handlePageClick);
        document.removeEventListener('mouseover', this.handlePageOver);
        document.removeEventListener('mouseout', this.handlePageOut);
    },
};
</script>
<style lang="scss">
.input-area-fullScreen {
    .pi {
        font-size: 1.3rem !important;
    }

    // 优化全屏模式下的元素样式
    .p-sidebar-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .p-sidebar-content {
        display: flex;
        flex-direction: column;
        height: calc(100% - 60px);
    }
}

// 添加现代按钮样式
.modern-button {
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    border-radius: 6px;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }

    .p-button-label {
        font-weight: 500;
    }

    &.p-button {
        &.p-button-info {
            background: linear-gradient(135deg, #42a5f5, #2196f3);
        }
        &.p-button-success {
            background: linear-gradient(135deg, #66bb6a, #43a047);
        }
        &.p-button-help {
            background: linear-gradient(135deg, #ab47bc, #8e24aa);
        }
        &.p-button-warning {
            background: linear-gradient(135deg, #ffb74d, #ff9800);
        }
        &.p-button-primary {
            background: linear-gradient(135deg, #7986cb, #3f51b5);
        }
        &.p-button-danger {
            background: linear-gradient(135deg, #ff3670, #e91e63);
        }
    }

    &.model-button {
        min-width: 140px;

        .pi-cog {
            color: #fff;
            animation: spin 15s linear infinite;
            opacity: 0.9;
        }

        &:hover .pi-cog {
            animation-duration: 3s;
        }

        span {
            color: #fff;
            font-weight: 500;
        }
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 添加输入区域的动画效果
.form-inner-focus {
    animation: form-focus 0.3s ease forwards;
}

@keyframes form-focus {
    0% {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }
    100% {
        box-shadow: 0 8px 30px rgba(99, 102, 241, 0.15);
        transform: translateY(-2px);
    }
}

// 优化发送按钮的动画
.sending-btn-animate {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}
</style>
<style lang="scss" scoped>
@import './index.scss';
.admin-contact {
    margin-top: 0.25rem;
    padding: 0.25rem 0;

    .text-500 {
        color: var(--text-color-secondary);
        font-size: 0.75rem;
        opacity: 0.8;

        i {
            font-size: 0.75rem;
            opacity: 0.8;
        }
    }

    .admin-list {
        .admin-tag {
            transition: all 0.2s ease;
            background-color: transparent !important;
            color: var(--text-color-secondary);
            padding: 0.15rem 0.5rem;
            font-size: 0.75rem;
            opacity: 0.8;

            &:hover {
                color: var(--primary-color);
                opacity: 1;
            }
        }
    }
}
</style>
