<template>
    <Transition name="slide-fade">
        <div v-if="visible" class="reply-complete-notification" @click="handleClick">
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="pi pi-check-circle"></i>
                </div>
                <div class="notification-text">
                    <div class="notification-title">{{ title }}</div>
                    <div class="notification-subtitle">{{ subtitle }}</div>
                </div>
                <div class="notification-close" @click.stop="handleClose">
                    <i class="pi pi-times"></i>
                </div>
            </div>
        </div>
    </Transition>
</template>

<script>
export default {
    name: 'Notification',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: 'AI 回答完成',
        },
        subtitle: {
            type: String,
            default: '点击查看',
        },
        autoClose: {
            type: Boolean,
            default: true,
        },
        closeDelay: {
            type: Number,
            default: 5000,
        },
    },
    emits: ['update:visible', 'click', 'close'],
    data() {
        return {
            autoCloseTimer: null,
        };
    },
    watch: {
        visible(newVal) {
            if (newVal && this.autoClose) {
                this.startAutoClose();
            } else {
                this.clearAutoClose();
            }
        },
    },
    methods: {
        handleClick() {
            this.$emit('click');
            this.close();
        },
        handleClose() {
            this.$emit('close');
            this.close();
        },
        close() {
            this.$emit('update:visible', false);
        },
        startAutoClose() {
            this.clearAutoClose();
            this.autoCloseTimer = setTimeout(() => {
                this.close();
            }, this.closeDelay);
        },
        clearAutoClose() {
            if (this.autoCloseTimer) {
                clearTimeout(this.autoCloseTimer);
                this.autoCloseTimer = null;
            }
        },
    },
    beforeUnmount() {
        this.clearAutoClose();
    },
};
</script>

<style lang="scss" scoped>
.reply-complete-notification {
    position: fixed;
    top: 80px;
    right: 30px;
    z-index: 9999;
    max-width: 320px;
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 50%, #bbf7d0 100%);
    border-radius: 16px;
    box-shadow:
        0 8px 32px rgba(34, 197, 94, 0.12),
        0 2px 12px rgba(34, 197, 94, 0.08),
        0 0 0 1px rgba(34, 197, 94, 0.1);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(34, 197, 94, 0.15);

    /* 清新的未读提示小绿点 */
    &::before {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        width: 12px;
        height: 12px;
        background: linear-gradient(135deg, #22c55e, #16a34a);
        border-radius: 50%;
        box-shadow:
            0 0 0 3px rgba(255, 255, 255, 0.9),
            0 2px 8px rgba(34, 197, 94, 0.3);
        z-index: 1;
        animation: pulse-green 2s infinite;
    }

    &:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 16px 48px rgba(34, 197, 94, 0.18),
            0 8px 20px rgba(34, 197, 94, 0.12),
            0 0 0 1px rgba(34, 197, 94, 0.2);
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
    }

    .notification-content {
        display: flex;
        align-items: center;
        padding: 16px 18px;
        color: #064e3b;
    }

    .notification-icon {
        margin-right: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background: linear-gradient(135deg, #a7f3d0, #6ee7b7);
        border-radius: 50%;
        box-shadow:
            0 4px 12px rgba(34, 197, 94, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);

        i {
            font-size: 1.3rem;
            color: #047857;
            filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.5));
        }
    }

    .notification-text {
        flex: 1;

        .notification-title {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #047857;
            letter-spacing: -0.01em;
        }

        .notification-subtitle {
            font-size: 12px;
            color: #059669;
            opacity: 0.85;
            font-weight: 500;
        }
    }

    .notification-close {
        margin-left: 12px;
        padding: 6px;
        border-radius: 50%;
        transition: all 0.2s ease;
        opacity: 0.6;
        background: rgba(34, 197, 94, 0.05);
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
            opacity: 1;
            background: rgba(34, 197, 94, 0.1);
            transform: scale(1.1);
        }

        i {
            font-size: 0.9rem;
            color: #059669;
        }
    }
}

/* 清新的脉动动画 */
@keyframes pulse-green {
    0% {
        box-shadow:
            0 0 0 3px rgba(255, 255, 255, 0.9),
            0 0 0 6px rgba(34, 197, 94, 0.2);
    }
    50% {
        box-shadow:
            0 0 0 3px rgba(255, 255, 255, 0.9),
            0 0 0 12px rgba(34, 197, 94, 0.1);
    }
    100% {
        box-shadow:
            0 0 0 3px rgba(255, 255, 255, 0.9),
            0 0 0 6px rgba(34, 197, 94, 0.2);
    }
}

/* 动画效果 */
.slide-fade-enter-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-leave-active {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-enter-from {
    transform: translateX(100%) scale(0.8);
    opacity: 0;
}

.slide-fade-leave-to {
    transform: translateX(100%) scale(0.8);
    opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .reply-complete-notification {
        right: 20px;
        top: 70px;
        max-width: 280px;

        .notification-content {
            padding: 14px 16px;
        }

        .notification-icon {
            width: 32px;
            height: 32px;
            margin-right: 12px;

            i {
                font-size: 1.1rem;
            }
        }

        .notification-text {
            .notification-title {
                font-size: 14px;
            }

            .notification-subtitle {
                font-size: 11px;
            }
        }
    }
}
</style>
